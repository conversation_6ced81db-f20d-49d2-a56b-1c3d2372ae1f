import exceljs from '@zurmokeeper/exceljs';
import QRcode from 'qrcode';

async function main() {
  const workbook = new exceljs.Workbook();
  const worksheet = workbook.addWorksheet('My Sheet');

  // 第一行标题
  let cell = worksheet.getCell(1, 1);
  cell.value = '陕西建工第十六建设有限公司';
  cell.font = {
    name: '仿宋',
    size: 12,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  worksheet.mergeCells(1, 1, 1, 9);
  worksheet.getRow(1).height = 15.6;

  // 第二行标题
  cell = worksheet.getCell(2, 1);
  cell.value = '领料单';
  cell.font = {
    name: '仿宋',
    size: 12,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  worksheet.mergeCells(2, 1, 2, 9);
  worksheet.getRow(2).height = 14.4;

  // 第三行标题
  cell = worksheet.getCell(3, 1);
  cell.value = '项目名称：西安航天城水厂建设工程（二期）项目EPC工程总承包';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.mergeCells(3, 1, 3, 7);

  cell = worksheet.getCell(3, 8);
  cell.value = '单据编码：HTSC-25-';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.mergeCells(3, 8, 3, 9);
  worksheet.getRow(3).height = 14.4;

  // 第四行标题
  cell = worksheet.getCell(4, 1);
  cell.value = '领料单位：';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.mergeCells(4, 1, 4, 3);

  cell = worksheet.getCell(4, 4);
  cell.value = '使用部位：';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.mergeCells(4, 4, 4, 7);

  cell = worksheet.getCell(4, 8);
  cell.value = '编制日期：';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.mergeCells(4, 8, 4, 9);
  worksheet.getRow(4).height = 14.4;

  // 第五行
  cell = worksheet.getCell(5, 1);
  cell.value = '材料名称';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(1).width = 13.4 + 0.69;

  cell = worksheet.getCell(5, 2);
  cell.value = '规格型号';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(2).width = 11.57 + 0.69;

  cell = worksheet.getCell(5, 3);
  cell.value = '计量\n单位';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center',
    wrapText: true
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(3).width = 6.22 + 0.69;

  cell = worksheet.getCell(5, 4);
  cell.value = '计划数量';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(4).width = 8.86 + 0.69;

  cell = worksheet.getCell(5, 5);
  cell.value = '实发数量';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(5).width = 8.75 + 0.69;

  cell = worksheet.getCell(5, 6);
  cell.value = '单价';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(6).width = 8.22 + 0.69;

  cell = worksheet.getCell(5, 7);
  cell.value = '金额\n（元）';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center',
    wrapText: true
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(7).width = 9.45 + 0.69;

  cell = worksheet.getCell(5, 8);
  cell.value = '成本挂接科目';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(8).width = 14.41 + 0.69;

  cell = worksheet.getCell(5, 9);
  cell.value = '备注';
  cell.font = {
    name: '仿宋',
    size: 10.5
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(9).width = 10.79 + 0.69;

  worksheet.getRow(5).height = 28;

  // 空白行
  for (let rorNo = 6; rorNo <= 20; rorNo++) {
    worksheet.getRow(rorNo).height = 23;
    for (let colNo = 1; colNo <= 9; colNo++) {
      cell = worksheet.getCell(rorNo, colNo);
      cell.value = '合计';
      cell.font = {
        name: '仿宋',
        size: 10
      };
      cell.alignment = {
        vertical: 'middle',
        horizontal: 'center'
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    }
  }

  // foot
  // worksheet.getRow(21).height = 30;
  // cell = worksheet.getCell(21, 1);
  // cell.value = '送货人：';
  // cell.font = {
  //   name: '宋体',
  //   size: 11
  // };

  // cell = worksheet.getCell(21, 2);
  // cell.value = '材料员：';
  // cell.font = {
  //   name: '宋体',
  //   size: 11
  // };
  // worksheet.mergeCells(21, 2, 21, 4);

  // cell = worksheet.getCell(21, 5);
  // cell.value = '施工员：';
  // cell.font = {
  //   name: '宋体',
  //   size: 11
  // };
  // worksheet.mergeCells(21, 5, 21, 6);

  // cell = worksheet.getCell(21, 7);
  // cell.value = '分包材料员：';
  // cell.font = {
  //   name: '宋体',
  //   size: 11
  // };
  // worksheet.mergeCells(21, 7, 21, 8);

  // 页脚
  worksheet.headerFooter.differentOddEven = false;
  worksheet.headerFooter.oddFooter =
    '&L&"仿宋"&10施工员：                                发料员：                                   领料员：							\n                                          &P/&N';
  // worksheet.pageSetup.horizontalCentered = true;
  // worksheet.pageSetup.verticalCentered = true;

  // 添加图片
  const base64Url = await QRcode.toDataURL('https://www.baidu.com');
  const imageId = workbook.addImage({
    // filename: './图片二维码.jpg.png',
    base64: base64Url,
    extension: 'png'
  });

  worksheet.addImage(imageId, {
    tl: {
      nativeCol: 8,
      nativeColOff: 1000,
      nativeRow: 0,
      nativeRowOff: 1000
    },
    ext: { width: 40, height: 40 },
    editAs: 'oneCell'
  });

  // 打印设置
  // 在每个打印页面上重复特定的行
  worksheet.pageSetup.printTitlesRow = '1:5';
  worksheet.pageSetup.paperSize = 20;
  worksheet.pageSetup.orientation = 'landscape';
  // worksheet.pageSetup.printTitlesRow = '21';
  // worksheet.getRow(8).addPageBreak();

  workbook.xlsx.writeFile('./output.xlsx').then(() => {
    console.log('Excel file generated successfully.');
  });
}

main();
