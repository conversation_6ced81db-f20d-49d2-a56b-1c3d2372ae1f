import exceljs from '@zurmokeeper/exceljs';
import QRcode from 'qrcode';

async function main() {
  const workbook = new exceljs.Workbook();
  const worksheet = workbook.addWorksheet('My Sheet');

  // 第一行标题
  let cell = worksheet.getCell(1, 1);
  cell.value = '陕西建工第十六建设有限公司';
  cell.font = {
    name: '仿宋',
    size: 14,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  worksheet.mergeCells(1, 1, 1, 8);
  worksheet.getRow(1).height = 27;

  // 第二行标题
  cell = worksheet.getCell(2, 1);
  cell.value = '物资进场验收记录';
  cell.font = {
    name: '仿宋',
    size: 14,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  worksheet.mergeCells(2, 1, 2, 8);
  worksheet.getRow(2).height = 27;

  // 第三行标题
  cell = worksheet.getCell(3, 1);
  cell.value = '项目名称：';
  cell.font = {
    name: '仿宋',
    size: 11
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };

  cell = worksheet.getCell(3, 6);
  cell.value = '单据编码：';
  cell.font = {
    name: '仿宋',
    size: 11
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.getRow(3).height = 23;

  // 第四行标题
  cell = worksheet.getCell(4, 1);
  cell.value = '供应商名称：';
  cell.font = {
    name: '仿宋',
    size: 11
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };

  cell = worksheet.getCell(4, 6);
  cell.value = '进场日期：';
  cell.font = {
    name: '仿宋',
    size: 11
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'left'
  };
  worksheet.getRow(4).height = 23;

  // 第五行
  cell = worksheet.getCell(5, 1);
  cell.value = '物资名称';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(1).width = 21.33 + 0.69;

  cell = worksheet.getCell(5, 2);
  cell.value = '规格型号';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(2).width = 21.33 + 0.69;

  cell = worksheet.getCell(5, 3);
  cell.value = '质量标准';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(3).width = 21.33 + 0.69;

  cell = worksheet.getCell(5, 4);
  cell.value = '计量单位';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(4).width = 9.33 + 0.69;

  cell = worksheet.getCell(5, 5);
  cell.value = '进场数量';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(5).width = 9.33 + 0.69;

  cell = worksheet.getCell(5, 6);
  cell.value = '实收数量';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(6).width = 9.33 + 0.69;

  cell = worksheet.getCell(5, 7);
  cell.value = '外观质量描述';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(7).width = 26.33 + 0.69;

  cell = worksheet.getCell(5, 8);
  cell.value = '备注';
  cell.font = {
    name: '仿宋',
    size: 10,
    bold: true
  };
  cell.alignment = {
    vertical: 'middle',
    horizontal: 'center'
  };
  cell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
  worksheet.getColumn(8).width = 6.89 + 0.69;

  worksheet.getRow(5).height = 30;

  // 空白行
  for (let rorNo = 6; rorNo <= 20; rorNo++) {
    worksheet.getRow(rorNo).height = 25;
    for (let colNo = 1; colNo <= 8; colNo++) {
      cell = worksheet.getCell(rorNo, colNo);
      cell.value = '';
      cell.font = {
        name: '仿宋',
        size: 10
      };
      cell.alignment = {
        vertical: 'middle',
        horizontal: 'center'
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    }
  }

  // foot
  worksheet.getRow(21).height = 30;
  cell = worksheet.getCell(21, 1);
  cell.value = '送货人：';
  cell.font = {
    name: '宋体',
    size: 11
  };

  cell = worksheet.getCell(21, 2);
  cell.value = '材料员：';
  cell.font = {
    name: '宋体',
    size: 11
  };
  worksheet.mergeCells(21, 2, 21, 4);

  cell = worksheet.getCell(21, 5);
  cell.value = '施工员：';
  cell.font = {
    name: '宋体',
    size: 11
  };
  worksheet.mergeCells(21, 5, 21, 6);

  cell = worksheet.getCell(21, 7);
  cell.value = '分包材料员：';
  cell.font = {
    name: '宋体',
    size: 11
  };
  worksheet.mergeCells(21, 7, 21, 8);

  // 添加图片
  const base64Url = await QRcode.toDataURL(
    'https://devtest-ewing.obs.cn-southwest-2.myhuaweicloud.com/0196aa30-0746-73c2-8a6e-c7beb7058ded/ecost/%E7%89%A9%E8%B5%84%E8%BF%9B%E5%9C%BA%E9%AA%8C%E6%94%B6%E5%8D%95_1752216029547.pdf?AccessKeyId=HPUACUFE1SM7BJGVFFBP&Expires=2382936030&Signature=mjUgLnDhait8ZxOBV4Mo4Ff6QNU%3D'
  );
  const imageId = workbook.addImage({
    // filename: './图片二维码.jpg.png',
    base64: base64Url,
    extension: 'png'
  });

  worksheet.addImage(imageId, {
    tl: {
      nativeCol: 6,
      nativeColOff: 1539240,
      nativeRow: 0,
      nativeRowOff: 74930
    },
    ext: { width: 72, height: 72 },
    editAs: 'oneCell'
  });

  // 打印设置
  // 在每个打印页面上重复特定的行
  worksheet.pageSetup.printTitlesRow = '1:5';
  worksheet.pageSetup.paperSize = 37;
  worksheet.pageSetup.orientation = 'landscape';
  // worksheet.pageSetup.printTitlesRow = '21';
  // worksheet.getRow(8).addPageBreak();

  workbook.xlsx.writeFile('./output.xlsx').then(() => {
    console.log('Excel file generated successfully.');
  });
}

main();
