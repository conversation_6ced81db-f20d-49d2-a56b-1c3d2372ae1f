// excel列宽设置偏移值
export const WIDTH_OFFSET = 0.69;

// 任务状态
export const TaskStatus = {
  DOING: 'doing',
  DONE: 'done',
  ERROR: 'error'
};

export const ExportFnMap = {
  sheetName: 'sheetNameExportFn'
};

export const PRODUCT_ECOST = 'ecost';

export const REDIS_TIMEOUT_30M = 60 * 30;

// 纸张大小 - 通过枚举值索引
export const PAPER_SIZE_BY_VALUE = {
  5: { width: 216, height: 356, no: 5 }, // Legal
  7: { width: 184, height: 267, no: 7 }, // Executive
  9: { width: 210, height: 297, no: 9 }, // A4
  11: { width: 148, height: 210, no: 11 }, // A5
  13: { width: 176, height: 250, no: 13 }, // B5
  20: { width: 105, height: 241, no: 20 }, // Envelope_10
  27: { width: 110, height: 220, no: 27 }, // Envelope_DL
  28: { width: 162, height: 229, no: 28 }, // Envelope_C5
  34: { width: 176, height: 250, no: 34 }, // Envelope_B5
  37: { width: 98, height: 191, no: 37 }, // Envelope_Monarch
  82: { width: 148, height: 200, no: 82 }, // Double_Japan_Postcard_Rotated
  119: { width: 197, height: 273, no: 119 } // K16_197x273_mm
};

// 纸张大小 - 通过名称索引
export const PAPER_SIZE = {
  A4: { width: 210, height: 297, no: 9 },
  Legal: { width: 216, height: 356, no: 5 },
  Executive: { width: 184, height: 267, no: 7 },
  A5: { width: 148, height: 210, no: 11 },
  B5: { width: 176, height: 250, no: 13 },
  Envelope_10: { width: 105, height: 241, no: 20 },
  Envelope_DL: { width: 110, height: 220, no: 27 },
  Envelope_C5: { width: 162, height: 229, no: 28 },
  Envelope_B5: { width: 176, height: 250, no: 34 },
  Envelope_Monarch: { width: 98, height: 191, no: 37 },
  Double_Japan_Postcard_Rotated: { width: 148, height: 200, no: 82 },
  K16_197x273_mm: { width: 197, height: 273, no: 119 }
};
