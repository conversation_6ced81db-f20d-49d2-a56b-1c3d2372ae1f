import {
  FileClientService,
  GenerateAccessUrlsArgs,
  RedisClient,
  RedisKeyBuilder,
  RedisService
} from '@ewing/infra-cloud-sdk';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import dayjs from 'dayjs';
import fs from 'fs';
import isEmpty from 'lodash.isempty';
import QRcode from 'qrcode';
import { firstValueFrom } from 'rxjs';
import { Readable } from 'stream';

import {
  ExportFnMap,
  PRODUCT_ECOST,
  TaskStatus
} from '@/common/constants/common.constant';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  ColumnConfig,
  ExportExcelConfig,
  ExportExcelContext,
  PageFooterConfig,
  QRCodeConfig,
  SheetConfig,
  SheetData
} from '../../excel.interface';
import { ExportExcelBuilder } from '../../utils/export-excel-builder';
import { TaskManager } from '../../utils/task-manager';

@Injectable()
export class ExportExcelService {
  private redisCli: RedisClient;
  private readonly logger = new Logger(ExportExcelService.name);

  constructor(
    private configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
    private readonly fileClientService: FileClientService
  ) {
    this.redisCli = this.redisService.getRedisCli();
  }

  // #region excel导出逻辑

  /**
   * excel导出
   * @param data Record<string, any>
   */
  async exportExcel(
    req: Request,
    reqUser: IReqUser,
    queryParams: Record<string, any>
  ) {
    const { tenantId, orgId, id: userId, nickname } = reqUser;
    const { type } = queryParams;

    const exportConfig = await this.getExportConfig(type);
    if (!exportConfig) {
      throw new BadRequestException('未找到导出配置');
    }

    const task = new TaskManager({
      tenantId,
      orgId,
      userId,
      type: queryParams.type,
      userName: nickname,
      redisCli: this.redisCli
    });
    task.update({
      status: TaskStatus.DOING,
      progressMessage: `正在导出excel...`
    });

    // 异步执行导出任务
    this.executeExport(
      {
        task,
        req,
        reqUser,
        type,
        exportConfig,
        ignoreSheet: [],
        sheetQrCodes: []
      },
      queryParams
    );

    return { taskId: task.getTaskId() };
  }

  private async executeExport(
    context: ExportExcelContext,
    queryParams: Record<string, any>
  ) {
    const builder = new ExportExcelBuilder(context.exportConfig, queryParams);
    try {
      // 将 builder 添加到 context 中
      context.builder = builder;
      // 创建workbook
      builder.buildWorkBook();

      // 循环添加工作表
      for (const [sheetName, sheetConfig] of Object.entries(
        context.exportConfig.sheets
      )) {
        if (context.ignoreSheet.includes(sheetName)) continue;

        // 有导出函数的sheet
        if (sheetConfig.exportFn) {
          const exportFnName =
            ExportFnMap[sheetConfig.exportFn as keyof typeof ExportFnMap];
          if (
            exportFnName &&
            typeof this[exportFnName as keyof this] === 'function'
          ) {
            // 调用exportFn
            await (
              this[exportFnName as keyof this] as (
                context: ExportExcelContext,
                sheetName: string,
                sheetConfig: SheetConfig
              ) => Promise<void>
            )(context, sheetName, sheetConfig);
          }
        } else {
          // 没有导出函数的sheet
          await this.processSheet(context, sheetName, sheetConfig);
        }
      }

      // 提交工作簿
      await builder.workbookCommit();

      // 上传临时文件
      const dataFile = builder.getDataFile();
      const fileKey = context.builder.getFileName();
      if (dataFile == null || fileKey == null) {
        throw new Error('没有数据文件');
      }
      const fileData = fs.createReadStream(dataFile);

      const { url } = await this.uploadAndAccessUrl({
        tenantId: context.reqUser.tenantId,
        data: fileData,
        key: fileKey
      });

      // 是否要转为pdf文件;
      await this.processConvertPdf(context, url);

      // 更新任务状态
      context.task.update({
        url,
        fileKey,
        status: TaskStatus.DONE,
        finishTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
      });
    } catch (error: any) {
      this.logger.error(`excel导出报错: ${error}`);
      context.task.update({
        status: TaskStatus.ERROR,
        errorMsg: error.message,
        finishTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
      });
    } finally {
      // 删除临时文件
      builder.deleteTempFile();
    }
  }

  // 处理单个工作表
  private async processSheet(
    context: ExportExcelContext,
    sheetName: string,
    sheetConfig: SheetConfig
  ) {
    const builder = context.builder as ExportExcelBuilder;
    // 创建工作表
    builder.initSheetContext();
    builder.buildWorkSheet(sheetName);
    if (sheetConfig.enablePrint) {
      builder.setPageSetting(sheetConfig.pageSetting);
    }

    context.task.update({
      status: TaskStatus.DOING,
      progressMessage: `正在导出excel...当前工作表为：${sheetName}`
    });

    const rpcData: Record<string, any> = await this.rpcGetData(
      context.req,
      sheetConfig.method,
      sheetConfig.serviceName,
      sheetConfig.path,
      this.pickProps(sheetConfig.queryProps, builder.getProps())
    );

    // 循环处理sheetData中的多个区域
    for (const sheetArea of sheetConfig.sheetData) {
      await this.processSheetArea(context, sheetConfig, sheetArea, rpcData);
    }

    // 添加二维码
    await this.processSheetQrCode(context, sheetConfig);

    builder.worksheetCommit();
  }

  // 处理工作表中的区域
  private async processSheetArea(
    context: ExportExcelContext,
    sheetConfig: SheetConfig,
    areaConfig: SheetData,
    rpcData: Record<string, any>
  ) {
    let titleConfig = areaConfig.titles;
    let headerConfig = areaConfig.headers;
    let columnConfig = areaConfig.columns;
    let footerConfig = areaConfig.footers;
    if (isEmpty(titleConfig) && !isEmpty(rpcData.titles)) {
      titleConfig = rpcData.titles;
    }
    if (isEmpty(headerConfig) && !isEmpty(rpcData.headers)) {
      headerConfig = rpcData.headers;
    }
    if (isEmpty(columnConfig) && !isEmpty(rpcData.columns)) {
      columnConfig = rpcData.columns;
    }
    if (isEmpty(footerConfig) && !isEmpty(rpcData.footers)) {
      footerConfig = rpcData.footers;
    }

    const builder = context.builder as ExportExcelBuilder;

    // 处理标题区
    if (titleConfig && !isEmpty(titleConfig)) {
      for (const tc of titleConfig) {
        builder.buildRow(tc, {});
      }
    }

    // 处理表头
    if (headerConfig && !isEmpty(headerConfig)) {
      for (const hc of headerConfig) {
        builder.buildRow(hc, {});
      }
    }
    if (columnConfig && !isEmpty(columnConfig)) {
      const tempColumnConfigs = columnConfig.map((item) => item.header);
      builder.buildRow(tempColumnConfigs, {});
    }

    // 处理数据区
    await this.processDataArea(context, areaConfig.columns, rpcData.records);

    // 处理尾部区
    if (footerConfig && !isEmpty(footerConfig)) {
      for (const fc of footerConfig) {
        builder.buildRow(fc, {});
      }
    }

    // 处理页脚区域
    if (sheetConfig.enablePrint && areaConfig.pageFooter) {
      this.processPageFooter(context, areaConfig.pageFooter);
    }
  }

  // 处理数据区
  private async processDataArea(
    context: ExportExcelContext,
    columnConfig?: ColumnConfig[],
    records: Record<string, any>[] = []
  ) {
    if (!records || isEmpty(records) || !columnConfig || isEmpty(columnConfig))
      return;

    const builder = context.builder as ExportExcelBuilder;

    const chunkSize = 1000;
    for (let i = 0; i < records.length; i += chunkSize) {
      const chunk = records.slice(i, i + chunkSize);
      await new Promise<void>((resolve, reject) => {
        setTimeout(() => {
          try {
            for (const record of chunk) {
              // 假设 builder.buildRow 内部可能抛出同步错误
              builder.buildRow(columnConfig, record);
            }
            resolve(); // 成功处理当前批次
          } catch (error: any) {
            const rejectReason =
              error instanceof Error ? error : new Error(String(error));
            reject(rejectReason); // 拒绝 Promise，将错误传递出去
          }
        }, 0);
      });
    }
  }

  // 处理页脚区域
  private processPageFooter(
    context: ExportExcelContext,
    pageFooter: PageFooterConfig
  ) {
    const builder = context.builder as ExportExcelBuilder;
    const { oddContent, evenContent } = pageFooter;
    builder.setPageFooter(pageFooter.differentOddEven, oddContent, evenContent);

    if (pageFooter.margin) {
      builder.setPageFooterMargin(
        ExportExcelBuilder.convertCmToInch(pageFooter.margin)
      );
    }

    // // 处理页脚边距，如果需要页脚紧贴数据区，需要计算页脚间距
    // if (
    //   pageFooter.vertical &&
    //   pageFooter.vertical === 'top' &&
    //   sheetConfig.pageSetting &&
    //   sheetConfig.pageSetting.paperSize
    // ) {
    //   const sheetHeight = builder.getSheetHeight();
    //   const paperSizeValue = sheetConfig.pageSetting.paperSize;
    //   let paperHeight =
    //     PAPER_SIZE_BY_VALUE[paperSizeValue as keyof typeof PAPER_SIZE_BY_VALUE]
    //       ?.height || PAPER_SIZE_BY_VALUE[9].height; // 默认使用 A4
    //   if (sheetConfig.pageSetting.orientation === 'landscape') {
    //     // 如果是纸张方向是横向，纸张的宽度变为高度
    //     paperHeight =
    //       PAPER_SIZE_BY_VALUE[
    //         paperSizeValue as keyof typeof PAPER_SIZE_BY_VALUE
    //       ]?.width || PAPER_SIZE_BY_VALUE[9].width; // 默认使用 A4
    //   }

    //   // 页面总高度 - 工作表上方占用总高度 - 指定的偏移量mt
    //   const footerHeight = ExportExcelBuilder.convertCmToInch(
    //     (paperHeight -
    //       sheetHeight -
    //       ExportExcelBuilder.convertPtToMM(pageFooter.mt)) /
    //       10
    //   );

    //   if (footerHeight > 0) {
    //     // 如果当前数据区已经超了一页
    //     builder.setPageFooterMargin(footerHeight);
    //   }
    // }
  }

  // 处理页签的二维码展示
  private async processSheetQrCode(
    context: ExportExcelContext,
    sheetConfig: SheetConfig
  ) {
    const builder = context.builder as ExportExcelBuilder;
    const qrCodeConfig = sheetConfig.qrCode || builder.getExportConfig().qrCode;
    if (!qrCodeConfig) return;

    const fileName = builder.getFileName() as string;

    const sheetIndex = builder.getSheetIndex();
    let newFileName = `${ExportExcelBuilder.removeExtension(fileName).filename}.${qrCodeConfig.type}`;
    if (qrCodeConfig.independentSheetCode) {
      newFileName = `${ExportExcelBuilder.removeExtension(fileName).filename}_${sheetIndex}.${qrCodeConfig.type}`;
    }

    const sheetQrCode = context.sheetQrCodes.find(
      (i) => i.fileKey === newFileName
    );

    // 防止重复生成url
    let url = sheetQrCode?.url;
    if (!url) {
      const { url: u } = await this.uploadAndAccessUrl({
        tenantId: context.reqUser.tenantId,
        data: '',
        key: newFileName,
        expires: 630720000
      });
      url = await this.getShortUrl(u);
    }

    // 防止重复添加
    if (!sheetQrCode) {
      context.sheetQrCodes.push({
        sheetIndex: qrCodeConfig.independentSheetCode ? sheetIndex : -1,
        url,
        fileKey: newFileName
      });
    }

    // 生成二维码
    const base64Url = await QRcode.toDataURL(url, {
      errorCorrectionLevel: 'Q',
      margin: 1,
      width: 500
    });
    builder.addWorksheetImage(
      {
        base64: base64Url,
        extension: 'png'
      },
      {
        tl: this.calculateQrCodePosition(context, qrCodeConfig),
        ext: { width: qrCodeConfig.width, height: qrCodeConfig.height }
      }
    );
  }

  private calculateQrCodePosition(
    context: ExportExcelContext,
    qrCodeConfig: QRCodeConfig
  ) {
    const builder = context.builder as ExportExcelBuilder;
    const worksheet = builder.getWorksheet();
    if (!worksheet) return;

    let nativeCol = 0;
    let nativeColOff = 0;
    const nativeRow = 0;
    let nativeRowOff = 0;
    if (qrCodeConfig.position === 'lt') {
      // 左上比较简单, 只用设置一个偏移值就行了
      if (qrCodeConfig.margins?.left) {
        nativeColOff = ExportExcelBuilder.convertPxToEMU(
          qrCodeConfig.margins.left
        );
      }
      if (qrCodeConfig.margins?.top) {
        nativeRowOff = ExportExcelBuilder.convertPxToEMU(
          qrCodeConfig.margins.top
        );
      }
    } else if (qrCodeConfig.position === 'rt') {
      const sheetColIndex = builder.getSheetColIndex();

      // 获取图片需要的列宽
      const imageWidth = ExportExcelBuilder.convertPxToChar(qrCodeConfig.width);

      // 从后往前找，看第几列开始能放下二维码
      let accumulatedWidth = 0;
      for (let i = sheetColIndex; i >= 1; i--) {
        const columnWidth = worksheet.getColumn(i).width || 0;
        accumulatedWidth += columnWidth;
        if (accumulatedWidth >= imageWidth) {
          nativeCol = i - 1;
          break;
        }
      }

      // 计算横向偏移
      let endColsWidth = 0;
      for (let i = nativeCol + 1; i <= sheetColIndex; i++) {
        endColsWidth += worksheet.getColumn(i).width || 0;
      }
      nativeColOff = ExportExcelBuilder.convertCharToPx(
        endColsWidth - imageWidth
      );
      if (qrCodeConfig.margins?.left) {
        nativeColOff = ExportExcelBuilder.convertPxToEMU(
          nativeColOff - qrCodeConfig.margins.left
        );
      }
      if (qrCodeConfig.margins?.top) {
        nativeRowOff = ExportExcelBuilder.convertPxToEMU(
          qrCodeConfig.margins.top
        );
      }
    }
    return {
      nativeCol,
      nativeColOff,
      nativeRow,
      nativeRowOff
    };
  }

  /**
   * excel转pdf
   */
  async processConvertPdf(context: ExportExcelContext, dataFileUrl: string) {
    const builder = context.builder as ExportExcelBuilder;
    const exportConfig = builder.getExportConfig();
    const qrConfig = exportConfig.qrCode;
    const fileName = builder.getFileName();
    if (!qrConfig || !fileName) return;

    if (exportConfig.disableWpsConvert) {
      context.task.update({
        qrCodeUrl: dataFileUrl,
        progressMessage: '当前进度: excel转pdf成功.'
      });
      return;
    }

    // 如果不为空，需要sheet页签单独转换格式
    let qrCodeUrl: string = '';
    if (!isEmpty(context.sheetQrCodes)) {
      for (const { sheetIndex, fileKey, url } of context.sheetQrCodes) {
        if (sheetIndex === -1) {
          qrCodeUrl = url; // 二维码附件obs地址

          // 把excel转为pdf，并返回地址
          const result = await this.rpcConvertFile(
            context,
            qrConfig.type,
            fileName,
            dataFileUrl
          );

          if (!result || !result.pdfs) {
            this.logger.error('excel转pdf失败');
          }

          if (result.pdfs && !isEmpty(result.pdfs)) {
            // 下载转换成功后的附件url，上传到obs上覆盖添加二维码时提前生成附件
            await this.downThirdPartyFileAndUpload(
              context,
              fileKey,
              result.pdfs[0].url
            );
          }
        }
      }
    }

    context.task.update({
      qrCodeUrl,
      progressMessage: '当前进度: excel转pdf成功.'
    });
  }

  // 获取导出配置
  private async getExportConfig(configKey: string) {
    // 读取excel导出配置
    const redisKey = new RedisKeyBuilder('excel')
      .newKey('excel-config')
      .build();
    const exportConfig = await this.redisCli.hget(redisKey, configKey);
    if (!exportConfig) {
      throw new Error('未找到导出配置');
    }

    return JSON.parse(exportConfig) as ExportExcelConfig;
  }

  // #endregion

  // #region excel-sheet导出函数

  // 导出函数：目录+多个子工作表
  private async sheetNameExportFn(
    context: ExportExcelContext,
    sheetName: string,
    sheetConfig: SheetConfig
  ) {
    const builder = context.builder as ExportExcelBuilder;
    const dynamicSheetInfo: Record<string, any> = await this.rpcGetData(
      context.req,
      sheetConfig.method,
      sheetConfig.serviceName,
      sheetConfig.path,
      this.pickProps(sheetConfig.sheetDesc?.props || [], builder.getProps())
    );

    // 处理目录sheet
    await this.processSheet(context, sheetName, sheetConfig);

    // 循环添加字表数据
    const sheetDesc = sheetConfig.sheetDesc;
    for (const dynamicSheet of dynamicSheetInfo.records) {
      const sheetKey = sheetDesc?.sheetKey || 'sheet';
      const exportConfig = builder.getExportConfig();
      const dynamicSheetConfig = exportConfig.sheets[sheetKey];
      // 动态导出的sheet配置应该被忽略防止重复
      context.ignoreSheet.push(sheetKey);

      if (dynamicSheetConfig) {
        await this.processSheet(
          context,
          dynamicSheet[sheetDesc?.sheetName || 'sheetName'],
          dynamicSheetConfig
        );
      }
    }
  }

  // #endregion

  // #region 辅助工具函数

  private pickProps(propDef: string[], props: Record<string, any>) {
    const result = propDef.reduce(
      (pre, cur) => {
        if (props[cur]) {
          pre[cur] = props[cur];
        }
        return pre;
      },
      {} as Record<string, any>
    );
    return result;
  }

  private async rpcGetData(
    req: Request,
    method: string,
    serviceName: string,
    path: string,
    props: any
  ): Promise<any> {
    const url = `${this.configService.get(`app.apiUrl.${serviceName}`)}${path}`;
    let result: any;
    if (method.toLocaleLowerCase() === 'get') {
      result = await firstValueFrom(
        this.httpService.get(url, {
          params: props,
          headers: { ...this.pickHeader(req) }
        })
      );
    } else if (method.toLocaleLowerCase() === 'post') {
      result = await firstValueFrom(
        this.httpService.post(url, props, {
          headers: { ...this.pickHeader(req) }
        })
      );
    }

    if (result.data) {
      return result.data;
    }
  }

  private pickHeader(req: Request): Record<string, string> {
    const headers: Record<string, string> = {};
    for (const [key, value] of Object.entries(req.headers)) {
      if (key === 'authorization' || key.startsWith('x-')) {
        headers[key] = value;
      }
    }
    return headers;
  }

  // rpc调用文件转换服务
  async rpcConvertFile(
    context: ExportExcelContext,
    type: string,
    filename: string,
    url: string
  ): Promise<Record<string, any>> {
    let result: any = await firstValueFrom(
      this.httpService.post(
        `${this.configService.get(`app.apiUrl.infraCloud`)}/wps/convert/sync`,
        { type, filename, url },
        {
          headers: this.pickHeader(context.req)
        }
      )
    );

    if (type === 'pdf') {
      result = result.data.result;
    }

    return result;
  }

  /**
   * 下载第三方文件并上传至obs
   */
  async downThirdPartyFileAndUpload(
    context: ExportExcelContext,
    key: string,
    url: string
  ) {
    const result = await firstValueFrom(
      this.httpService.get(url, {
        responseType: 'stream'
      })
    );

    await this.fileClientService.upload({
      tenantId: context.reqUser.tenantId,
      product: PRODUCT_ECOST,
      data: result.data,
      key
    });
  }

  /**
   * 上传文件并且返回授权url
   */
  async uploadAndAccessUrl(params: {
    tenantId: string;
    data: string | Readable;
    key: string;
    isTempFile?: boolean;
    expires?: number;
  }) {
    const { tenantId, data, key, isTempFile = false, expires } = params;

    await this.fileClientService.upload({
      tenantId: tenantId,
      product: PRODUCT_ECOST,
      data,
      isTempFile,
      key
    });

    // 生成文件下载url
    const accessUrlArgs: GenerateAccessUrlsArgs = {
      tenantId: tenantId,
      product: PRODUCT_ECOST,
      isTempFile,
      keys: [key]
    };
    if (expires) {
      accessUrlArgs.expires = expires;
    }
    const urls = await this.fileClientService.generateAccessUrls(accessUrlArgs);
    const url = urls[0][key];

    return { url };
  }

  async getShortUrl(longUrl: string): Promise<string> {
    try {
      const result: any = await firstValueFrom(
        this.httpService.post(
          `${this.configService.get(`app.apiUrl.infraCloud`)}/short-url/create`,
          {},
          {
            params: { url: longUrl }
          }
        )
      );
      if (result.data.url) {
        return result.data.url;
      }
    } catch (error: any) {
      throw new Error(`获取短连接出错：${error.message}`);
    }

    return longUrl;
  }

  // #endregion

  // #region 获取excel导出进度
  /**
   * excel导出进度
   */
  async getExportExcelProgress(key: string): Promise<Record<string, any>> {
    const data = await this.redisCli.get(key);
    if (data) {
      return JSON.parse(data);
    }
    return {};
  }

  // #endregion
}
