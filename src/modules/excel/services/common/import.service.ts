import {
  FileClientService,
  RedisClient,
  RedisKeyBuilder,
  RedisService
} from '@ewing/infra-cloud-sdk';
import { BadRequestException, Injectable } from '@nestjs/common';
import ExcelJS, { CellValue } from '@zurmokeeper/exceljs';
import dayjs from 'dayjs';

import { PRODUCT_ECOST } from '@/common/constants/common.constant';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  ImportExcelColumnType,
  ImportExcelConfig,
  ImportExcelSheetConfig
} from '../../excel.interface';

@Injectable()
export class ImportExcelService {
  private redisCli: RedisClient;
  constructor(
    private readonly redisService: RedisService,
    private readonly fileClientService: FileClientService
  ) {
    this.redisCli = this.redisService.getRedisCli();
  }

  async importExcel(
    req: Request,
    reqUser: IReqUser,
    data: Record<string, any>
  ): Promise<Record<string, any[]>> {
    const { tenantId } = reqUser;
    const { type } = data;

    const importConfig: ImportExcelConfig = await this.getImportConfig(type);
    if (!importConfig) {
      throw new BadRequestException('未找到导入配置');
    }

    // 下载文件
    const dataStream = await this.fileClientService.download({
      tenantId,
      product: PRODUCT_ECOST,
      key: data.fileKey,
      isTempFile: true,
      SaveAsStream: true
    });
    // const dataStream = fs.createReadStream(
    //   '/Users/<USER>/Downloads/进场验收单导入模版.xlsx'
    // );

    // 读取excel文件
    const options: Partial<ExcelJS.stream.xlsx.WorkbookStreamReaderOptions> =
      {};
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(
      dataStream,
      options
    );

    const result: Record<string, any[]> = {};
    for await (const worksheetReader of workbookReader) {
      const worksheetName = (worksheetReader as any).name;
      const sheetImportConfig = importConfig[worksheetName];
      if (!sheetImportConfig) continue;

      result[worksheetName] = [];
      await this.processWorkSheetRows(
        sheetImportConfig,
        worksheetReader,
        result[worksheetName]
      );
    }

    return result;
  }

  async processWorkSheetRows(
    importSheetConfig: ImportExcelSheetConfig,
    worksheetReader: ExcelJS.stream.xlsx.WorksheetReader,
    workSheetData: Record<string, any>[]
  ) {
    let rowIndex = 1;
    const dataStartRow = importSheetConfig.dataStartRow;
    const dataEndRow = importSheetConfig.dataEndRow;
    const columns = importSheetConfig.columns;
    for await (const row of worksheetReader) {
      // 如果存在数据区结束行
      if (dataEndRow && rowIndex > dataEndRow) break;

      // 读取数据区
      const rowData: Record<string, any> = {
        rowNo: rowIndex
      };
      if (rowIndex >= dataStartRow) {
        // 获取这一行每一列的值
        for (let i = 1; i <= columns.length; i++) {
          const column = columns[i - 1];
          const cellvalue = row.values
          const cell = row.getCell(i);
          const columnType = column.type;
          const cellValue = this.getCellValue(cell, columnType);
          rowData[column.key] = cellValue;
        }

        workSheetData.push(rowData);
      }

      rowIndex++;
    }
  }

  private getCellValue(cell: ExcelJS.Cell, columnType: ImportExcelColumnType) {
    let value: CellValue = cell.value;

    if (columnType === 'date') {
      if (typeof value === 'string') {
        value = dayjs(value).format('YYYY-MM-DD');
      } else if (typeof value === 'number') {
        value = dayjs(this.excelDateToJSDate(value)).format('YYYY-MM-DD');
      }
    } else if (columnType === 'number') {
      value = Number(value);
    } else {
      value = value
        ?.toString()
        .trim()
        .replace(/[\r\n]+/g, '');
    }

    return value;
  }

  private excelDateToJSDate(serial: number): Date {
    const utc = Date.UTC(1899, 11, 30); // Excel 从 1900-01-01 开始，但存在 bug，需减1天再加1
    const days = serial;
    const msPerDay = 24 * 60 * 60 * 1000;
    return new Date(utc + days * msPerDay);
  }

  // 获取导出配置
  private async getImportConfig(configKey: string) {
    // 读取excel导出配置
    const redisKey = new RedisKeyBuilder('excel')
      .newKey('excel-config')
      .build();
    const exportConfig = await this.redisCli.hget(redisKey, configKey);
    if (!exportConfig) {
      throw new Error('未找到导出配置');
    }

    return JSON.parse(exportConfig) as ImportExcelConfig;
  }
}
