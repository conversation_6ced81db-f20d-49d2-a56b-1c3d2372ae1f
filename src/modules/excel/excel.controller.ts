import { Body, Controller, Get, Post, Query, Req } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { ExportExcelService } from './services/common/export.service';
import { ImportExcelService } from './services/common/import.service';

@ApiTags('excel导入导出')
@Controller('excel')
export class ExcelController {
  constructor(
    private readonly exportExcelService: ExportExcelService,
    private readonly importExcelService: ImportExcelService
  ) {}

  @ApiOperation({ summary: '通用excel导出接口' })
  @Post('common/export-excel')
  @ApiResponse({
    status: 200,
    description: '返回任务id'
  })
  async exportExcel(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Body() data: Record<string, any>
  ) {
    return await this.exportExcelService.exportExcel(req, reqUser, data);
  }

  @ApiOperation({ summary: '通用excel导入接口' })
  @Post('common/import-excel')
  @ApiResponse({
    status: 200,
    description: '返回任务id'
  })
  async importExcel(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Body() data: Record<string, any>
  ) {
    return await this.importExcelService.importExcel(req, reqUser, data);
  }

  @ApiOperation({ summary: '通用excel导出进度接口' })
  @Get('common/export-excel/progress')
  @ApiResponse({
    status: 200,
    description: '返回任务进度信息'
  })
  async getExportExcelProgress(@Query('taskId') taskId: string) {
    return await this.exportExcelService.getExportExcelProgress(taskId);
  }
}
