import ExcelJS from '@zurmokeeper/exceljs';
import fs from 'fs';
import os from 'os';
import path from 'path';

import { WIDTH_OFFSET } from '@/common/constants/common.constant';

import { CellConfig, ExportExcelConfig } from '../excel.interface';

export class ExportExcelBuilder {
  private wb: ExcelJS.Workbook | ExcelJS.stream.xlsx.WorkbookWriter | null;
  private dataFile: string | null;
  private fileName: string | null;
  private exportConfig: ExportExcelConfig;
  private props: Record<string, any> = {};

  private worksheet: ExcelJS.Worksheet | null;
  private sheetIndex: number = 0;
  private sheetColIndex: number = 1;
  private sheetRowIndex: number = 1;
  private sheetWidth: number = 0; // 工作表内容宽度(单位mm)
  private sheetHeight: number = 0; // 工作表内容高度(单位mm)

  // 记录列宽和行高是否被重复设置
  private columnWidthSet: Set<number> = new Set();
  private columnHeightSet: Set<number> = new Set();

  constructor(exportConfig: ExportExcelConfig, props: Record<string, any>) {
    this.wb = null; // 工作簿
    this.dataFile = null; // 数据文件地址
    this.fileName = null; // excel文件名

    this.exportConfig = exportConfig; // excel导出配置

    this.props = props;

    this.initSheetContext();
  }

  // 初始化sheet上下文（多页签导出时用于初始化页签上下文）
  initSheetContext() {
    this.worksheet = null;
    this.sheetColIndex = 1; // 当前sheet列位置
    this.sheetRowIndex = 1; // 当前sheet行位置
    this.sheetWidth = 0;
    this.sheetHeight = 0;
    this.columnWidthSet = new Set();
    this.columnHeightSet = new Set();
  }

  // 创建工作簿
  buildWorkBook(fileName?: string) {
    const namePrefix = this.fillPropValue(this.exportConfig.fileNamePrefix);
    this.fileName = fileName || `${namePrefix}_${new Date().getTime()}.xlsx`;
    // 文件名不能包含 \/:*?"<>| 统一替换为 _
    this.fileName = this.fileName.replace(/[\\/:*?"<>|]/g, '_');

    this.dataFile = path.join(os.tmpdir(), this.fileName);

    if (this.exportConfig.useStream) {
      this.wb = new ExcelJS.stream.xlsx.WorkbookWriter({
        useStyles: true,
        filename: this.dataFile
      });
    } else {
      this.wb = new ExcelJS.Workbook();
    }
  }

  // 创建工作表
  buildWorkSheet(name: string) {
    if (!this.wb) return;

    this.sheetIndex++;
    if (!name) {
      // excel工作表名称不能为空
      name = `Sheet${this.sheetIndex}`;
    }
    if (name.length > 31) {
      // excel工作表名称不能超过31个字符
      name = name.slice(0, 31);
    }

    // 替换excel中的非法字符
    name = name.replace(/:/g, '：'); // 冒号 → 全角冒号（U+FF1A）
    name = name.replace(/\//g, '╱'); // 斜杠 → Unicode 斜线（U+2571）
    name = name.replace(/\x2A/g, '※'); // 星号 * → 注记符号（U+203B）
    name = name.replace(/\x3F/g, '？'); // 问号 ? → 全角问号（U+FF1F）
    name = name.replace(/\x5C/g, '╲'); // 反斜杠 \ → Unicode 反斜线（U+2572）

    this.worksheet = this.wb.addWorksheet(name);
  }

  // 工作表页面设置
  setPageSetting(pageSetting?: ExcelJS.PageSetup) {
    if (!this.worksheet || !pageSetting) return;

    // 纸张大小
    if (pageSetting.paperSize) {
      this.worksheet.pageSetup.paperSize = pageSetting.paperSize;
    }

    // 纸张方向
    if (pageSetting.orientation) {
      this.worksheet.pageSetup.orientation = pageSetting.orientation;
    }

    // 页边距
    if (pageSetting.margins) {
      if (!this.worksheet.pageSetup.margins) return;

      if (pageSetting.margins.top) {
        this.worksheet.pageSetup.margins.top =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.top);
      }
      if (pageSetting.margins.right) {
        this.worksheet.pageSetup.margins.right =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.right);
      }
      if (pageSetting.margins.bottom) {
        this.worksheet.pageSetup.margins.bottom =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.bottom);

        this.sheetHeight += pageSetting.margins.bottom * 10;
      }
      if (pageSetting.margins.left) {
        this.worksheet.pageSetup.margins.left =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.left);
      }
      if (pageSetting.margins.header) {
        this.worksheet.pageSetup.margins.header =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.header);
      }
      if (pageSetting.margins.footer) {
        this.worksheet.pageSetup.margins.footer =
          ExportExcelBuilder.convertCmToInch(pageSetting.margins.footer);
      }
    }

    // 打印区域
    if (pageSetting.printArea) {
      this.worksheet.pageSetup.printArea = pageSetting.printArea;
    }

    // 固定打印标题
    if (pageSetting.printTitlesRow) {
      this.worksheet.pageSetup.printTitlesRow = pageSetting.printTitlesRow;
    }

    // 将所有的列/行放在一页打印
    if (
      pageSetting.fitToPage &&
      (pageSetting.fitToWidth || pageSetting.fitToHeight)
    ) {
      this.worksheet.pageSetup.fitToPage = true;
      if (pageSetting.fitToWidth) {
        this.worksheet.pageSetup.fitToWidth = pageSetting.fitToWidth;
      }
      if (pageSetting.fitToHeight) {
        this.worksheet.pageSetup.fitToHeight = pageSetting.fitToHeight;
      }
    }
  }

  // 页脚内容设置
  setPageFooter(
    differentOddEven: boolean,
    oddContent?: string,
    evenContent?: string
  ) {
    if (!this.worksheet) return;

    this.worksheet.headerFooter.differentOddEven = differentOddEven;

    this.worksheet.headerFooter.oddFooter = oddContent;
    if (differentOddEven) {
      this.worksheet.headerFooter.evenFooter = evenContent;
    }
  }

  // 页脚边距设置
  setPageFooterMargin(value: number) {
    if (!this.worksheet || !this.worksheet.pageSetup.margins) return;

    this.worksheet.pageSetup.margins.footer = value;
  }

  // 添加二维码图片
  addWorksheetImage(image: ExcelJS.Image, range: any, imageId?: number) {
    if (!this.wb || !this.worksheet || this.exportConfig.useStream) return;

    if (!imageId) {
      // 如果没有imageId，给工作簿添加图片
      imageId = this.wb.addImage(image);
    }
    this.worksheet.addImage(imageId, range);
  }

  // 添加行
  buildRow(columns: CellConfig[], rowRecord: Record<string, any>) {
    if (!this.worksheet) return;

    const row: ExcelJS.Row = this.worksheet.getRow(this.sheetRowIndex);

    this.sheetColIndex = 1;
    for (const col of columns) {
      this.processCell(col, row, rowRecord);
    }
    // 行++
    this.sheetRowIndex++;

    if (this.exportConfig.useStream) {
      row.commit();
    }
  }

  processCell(
    cellConfig: CellConfig,
    row: ExcelJS.Row,
    rowRecord: Record<string, any>
  ) {
    if (!this.worksheet) return;

    const cell = row.getCell(this.sheetColIndex);

    // 单元格值
    cell.value = this.getCellValue(cellConfig, rowRecord);

    // 设置样式
    let style = cellConfig.style;
    if ('header' in cellConfig) {
      // columns，如果数据区没有定义样式，数据区样式复用表头样式
      style = Object.assign({}, cellConfig.header.style, style);
    }
    if (style) {
      this.setCellStyle(cell, style);
    }

    // 设置行高，行高按理说也只应该设置一次。所有列共用行高
    if (cellConfig.height && !this.columnHeightSet.has(this.sheetRowIndex)) {
      row.height = cellConfig.height;

      // 防止重复设置行高
      this.columnHeightSet.add(this.sheetRowIndex);
      // 记录工作表内容高度
      this.sheetHeight += ExportExcelBuilder.convertPtToMM(cellConfig.height);
    }

    // 设置列宽，列宽只应该在columns列配置，且每一列的列宽只应该被设置一次
    if (
      'width' in cellConfig &&
      cellConfig.width &&
      !this.columnWidthSet.has(this.sheetColIndex)
    ) {
      const width = cellConfig.width + WIDTH_OFFSET;
      this.worksheet.getColumn(this.sheetColIndex).width = width;

      // 防止重复设置列宽
      this.columnWidthSet.add(this.sheetColIndex);
      // 记录工作表内容宽度
      this.sheetWidth += ExportExcelBuilder.convertCharToMM(width);
    }

    // 合并单元格
    if ('colSpan' in cellConfig || 'rowSpan' in cellConfig) {
      const colCount = this.setMergeCell(
        cellConfig.rowSpan,
        cellConfig.colSpan
      );
      this.sheetColIndex += colCount;
    } else {
      // 列++
      this.sheetColIndex++;
    }
  }

  private setCellStyle(cell: ExcelJS.Cell, style: ExcelJS.Style) {
    if (style?.font) {
      cell.font = style.font;
    }
    if (style?.alignment) {
      cell.alignment = style.alignment;
    }
    if (style?.border) {
      cell.border = style.border;
    }
    if (style?.fill) {
      cell.fill = style.fill;
    }
    if (style?.numFmt) {
      cell.numFmt = style.numFmt;
    }
  }

  private setMergeCell(
    rowSpan: number | undefined,
    colSpan: number | undefined
  ): number {
    const rowCount = rowSpan || 1;
    const colCount = colSpan || 1;

    this.worksheet?.mergeCells(
      this.sheetRowIndex,
      this.sheetColIndex,
      this.sheetRowIndex + rowCount - 1,
      this.sheetColIndex + colCount - 1
    );

    return colCount;
  }

  private getCellValue(cellConfig: CellConfig, rowRecord: Record<string, any>) {
    let value: string = '';
    // 处理渲染title、header、columns、footer的单元格值为字符串的情况
    if ('text' in cellConfig) {
      value = this.fillPropValue(cellConfig.text);
    }

    // 处理渲染数据区，从数据源中获取数据
    if ('key' in cellConfig && rowRecord[cellConfig.key]) {
      value = rowRecord[cellConfig.key];
    }

    return value;
  }

  private fillPropValue(content: string) {
    if (!content) {
      return '';
    }
    const props = Array.from(new Set(content.match(/\{[^}]+\}/g)));
    props.forEach((prop) => {
      const reg = new RegExp(prop, 'g');
      prop = prop.replace('{', '').replace('}', '');
      const value = this.props[prop];
      content = content.replace(reg, value);
    });
    return content;
  }

  worksheetCommit() {
    if (this.exportConfig.useStream) {
      this.worksheet?.commit();
    }
  }

  async workbookCommit() {
    if (this.exportConfig.useStream && this.wb && 'commit' in this.wb) {
      await this.wb.commit();
    } else if (this.wb && 'xlsx' in this.wb) {
      await this.wb.xlsx.writeFile(this.dataFile as string);
    }
  }

  // 删除临时文件
  deleteTempFile() {
    if (this.dataFile && fs.existsSync(this.dataFile)) {
      fs.unlinkSync(this.dataFile);
    }
  }

  // getter
  getWorksheet = () => this.worksheet;
  getDataFile = () => this.dataFile;
  getFileName = () => this.fileName;
  getExportConfig = () => this.exportConfig;
  getProps = () => this.props;
  getSheetWidth = () => this.sheetWidth;
  getSheetHeight = () => this.sheetHeight;
  getSheetIndex = () => this.sheetIndex;
  getSheetColIndex = () => this.sheetColIndex;

  // 工具函数
  static removeExtension(fullFilename: string): {
    filename: string;
    extension: string;
  } {
    const lastDotIndex = fullFilename.lastIndexOf('.');

    // 场景1：没有点，或者点是第一个字符（例如 ".bashrc" 这样的隐藏文件）
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return {
        filename: fullFilename,
        extension: ''
      };
    }

    // 场景2：有后缀的普通文件
    const filename = fullFilename.substring(0, lastDotIndex); // 从开头到最后一个点之前
    const extension = fullFilename.substring(lastDotIndex + 1); // 从最后一个点之后到结尾

    return {
      filename: filename,
      extension: extension
    };
  }

  // 磅->毫米
  static convertPtToMM(pt: number): number {
    return (25.4 / 72) * pt;
  }

  // 毫米->磅
  static convertMMToPt(mm: number): number {
    return (72 / 25.4) * mm;
  }

  // 字符->毫米
  static convertCharToMM(ca: number): number {
    return 1.97 * ca;
  }

  // 毫米->字符
  static convertMMToChar(mm: number): number {
    return mm / 1.97;
  }

  // 磅->像素
  static convertPtToPx(pt: number): number {
    return (96 / 72) * pt;
  }

  // 像素->磅
  static convertPxToPt(px: number): number {
    return (72 / 96) * px;
  }

  // 字符->像素
  static convertCharToPx(cha: number): number {
    return 7 * cha;
  }

  // 像素->字符
  static convertPxToChar(px: number): number {
    return px / 7;
  }

  // 像素->EMU
  static convertPxToEMU(px: number): number {
    return px * 9525;
  }

  // 英寸->厘米
  static convertInchToCm(inch: number): number {
    return inch * 2.54;
  }

  // 厘米->英寸
  static convertCmToInch(cm: number): number {
    return cm / 2.54;
  }
}
