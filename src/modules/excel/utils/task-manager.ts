import { RedisClient, RedisKeyBuilder } from '@ewing/infra-cloud-sdk';
import dayjs from 'dayjs';

import { REDIS_TIMEOUT_30M } from '@/common/constants/common.constant';

export class TaskManager {
  private taskId: string;
  private redisCli: RedisClient;
  private taskInfo: Record<string, any>;

  constructor(initParams: Record<string, any>) {
    const {
      tenantId,
      orgId,
      userId,
      type,
      userName,
      status,
      progressMessage,
      redisCli
    } = initParams;

    this.taskId = this.generateTaskId(tenantId, orgId, type);

    this.taskInfo = {};
    this.taskInfo.orgId = orgId;
    this.taskInfo.userId = userId;
    this.taskInfo.tenantId = tenantId;
    this.taskInfo.status = status;
    this.taskInfo.startTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    this.taskInfo.finishTime = null;
    this.taskInfo.errorMsgs = [];
    this.taskInfo.userName = userName;

    this.taskInfo.progressMessage = progressMessage;

    this.redisCli = redisCli;
  }

  // 更新任务进度信息
  async update(info: Record<string, string>): Promise<void> {
    if (info.status) {
      this.taskInfo.status = info.status;
    }
    if (info.finishTime) {
      this.taskInfo.finishTime = info.finishTime;
    }
    if (info.errorMsg) {
      this.taskInfo.errorMsgs.push(info.errorMsg);
    }
    if (info.progressMessage) {
      this.taskInfo.progressMessage = info.progressMessage;
    }
    if (info.url) {
      this.taskInfo.url = info.url;
    }
    if (info.fileKey) {
      this.taskInfo.fileKey = info.fileKey;
    }
    if (info.qrCodeUrl) {
      this.taskInfo.qrCodeUrl = info.qrCodeUrl;
    }

    await this.redisCli.setex(
      this.taskId,
      REDIS_TIMEOUT_30M,
      JSON.stringify(this.taskInfo)
    );
  }

  getTaskId(): string {
    return this.taskId;
  }

  // 获取任务进度信息
  async getTaskProgressInfo(): Promise<Record<string, any>> {
    const taskInfo = await this.redisCli.get(this.taskId);
    return taskInfo ? JSON.parse(taskInfo) : {};
  }

  // 生成taskId
  private generateTaskId(
    tenantId: string,
    orgId: string,
    type: string
  ): string {
    const taskId = new RedisKeyBuilder('excel')
      .newKey('task-progress')
      .field('tenantId', tenantId)
      .field('orgId', orgId)
      .field('type', type)
      .field('dateTime', new Date().getTime())
      .build();

    return taskId;
  }
}
