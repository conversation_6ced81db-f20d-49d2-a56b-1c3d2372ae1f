import { FileModule } from '@ewing/infra-cloud-sdk';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { ExcelController } from './excel.controller';
import { ExportExcelService } from './services/common/export.service';
import { ImportExcelService } from './services/common/import.service';

@Module({
  imports: [HttpModule, FileModule],
  controllers: [ExcelController],
  providers: [ExportExcelService, ImportExcelService]
})
export class ExcelModule {}
