import Excel from '@zurmokeeper/exceljs';

import { IReqUser } from '@/common/interfaces/req-user.interface';

import { ExportExcelBuilder } from './utils/export-excel-builder';
import { TaskManager } from './utils/task-manager';

// Excel配置相关的类型定义
// #region 导出定义

export interface TitleConfig {
  text: string;

  style?: Excel.Style;

  colSpan?: number;
  rowSpan?: number;

  height?: number;
}

export interface HeaderConfig {
  text: string;

  style?: Excel.Style;

  colSpan?: number;
  rowSpan?: number;

  height?: number;
}

export interface ColumnConfig {
  header: HeaderConfig;

  // 设置数据源的key
  key: string;

  // 这是数据区的样式配置
  style?: Excel.Style;

  // 列宽只有列定义才可以设置
  width?: number;

  // 这里的高度设置对数据区生效
  height?: number;
}

export interface FooterConfig {
  text: string;

  style?: Excel.Style;

  colSpan?: number;
  rowSpan?: number;

  height?: number;
}

export interface PageFooterConfig {
  // 奇偶页脚是否不同
  differentOddEven: boolean;

  /**
   * 页脚垂直布局
   * top 数据区下方
   * default 默认位置
   */
  vertical: 'top' | 'default';

  // 奇数页页脚内容
  // 见 https://github.com/exceljs/exceljs/blob/master/README_zh.md #脚本命令
  oddContent?: string;

  // 偶数页页脚内容
  evenContent?: string;

  // 页脚的下边距 单位cm
  margin?: number;
}

// 工作表数据接口
export interface SheetData {
  // 标题定义
  titles?: TitleConfig[][];

  /**
   * 表头定义，可以为空
   * 常用语定义多级列头，一级列头使用columns就足够了
   */
  headers?: HeaderConfig[][];

  /**
   * 列定义，必填项
   */
  columns: ColumnConfig[];

  // 尾部数据定义
  footers?: FooterConfig[][];

  // 页脚数据定义
  pageFooter?: PageFooterConfig;
}

// 工作表描述接口
export interface SheetDesc {
  sheetName: string;
  sheetKey: string;
  props: string[];
}

export interface QRCodeConfig {
  /**
   * 二维码文件类型
   */
  type: 'pdf' | 'png';

  // 二维码宽度，单位px
  width: 72;
  // 二维码高度，单位px
  height: 72;

  /**
   * 二维码位置
   * lt 左上角
   * rt 右上角
   */
  position: 'lt' | 'rt';

  // 二维码边距,单位px
  margins?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };

  /**
   * 是否为当前页单独生成二维码
   */
  independentSheetCode?: boolean;
}

// 工作表配置接口
export interface SheetConfig {
  // 调用服务名称
  serviceName: string;

  // 调用方法
  method: 'get' | 'post';

  // 请求路由
  path: string;

  // 请求参数
  queryProps: string[];

  // 导出函数
  exportFn?: string;

  sheetDesc?: SheetDesc;

  sheetData: SheetData[];

  // 是否启用打印格式
  enablePrint?: boolean;

  /**
   * 页面参数
   * margins单位为cm
   */
  pageSetting?: Excel.PageSetup;

  /**
   * 如果有值，代表要生成二维码
   */
  qrCode?: QRCodeConfig;
}

// Excel导出配置接口定义
export interface ExportExcelConfig {
  // 文件名前缀
  fileNamePrefix: string;

  // 是否启用流式导出
  useStream: boolean;

  // 工作表的配置
  sheets: Record<string, SheetConfig>;

  /**
   * 如果有值，代表要生成二维码
   */
  qrCode?: QRCodeConfig;

  // 是否禁用wps文件转换api，防止测试环境过度使用
  disableWpsConvert?: boolean;
}

export type CellConfig =
  | ColumnConfig
  | HeaderConfig
  | FooterConfig
  | TitleConfig;

export interface ExportExcelContext {
  task: TaskManager;

  req: Request;

  reqUser: IReqUser;

  type: string;

  exportConfig: ExportExcelConfig;

  builder?: ExportExcelBuilder;

  /**
   * 忽略的页签，用户处理目录多子级导出时，子级sheetData被重复导出的场景
   */
  ignoreSheet: string[];

  /**
   * QRCodeConfig的independentSheetCode为true，为sheet也单独生成了附件和二维码
   * 上传文件时，需要单页单独转换并上传
   */
  sheetQrCodes: {
    sheetIndex: number; // 如果sheetIndex为-1，就是不分页码
    url: string;
    fileKey: string;
  }[];

  [key: string]: any;
}

// #endregion

// #region 导入定义

export type ImportExcelColumnType = 'date' | 'number' | 'string';

export interface ImportExcelColumnConfig {
  // 列头名称
  col: string;

  // 数据字段key值
  key: string;

  // 当前列的值类型
  type: ImportExcelColumnType;
}

export interface ImportExcelSheetConfig {
  // 数据区开始行
  dataStartRow: number;

  // 数据区结束行
  dataEndRow?: number;

  // 列头所在行
  columnsRow: number;

  // 列头配置
  columns: ImportExcelColumnConfig[];
}

export interface ImportExcelConfig {
  // key是sheet工作表名称
  [key: string]: ImportExcelSheetConfig;
}

// #endregion
