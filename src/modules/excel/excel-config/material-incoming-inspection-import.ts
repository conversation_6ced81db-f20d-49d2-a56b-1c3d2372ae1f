console.log(
  JSON.stringify({
    物资进场验收单: {
      columnsRow: 1,
      columns: [
        {
          col: '进场日期',
          key: 'siteEntryDate',
          type: 'date'
        },
        {
          col: '供应商名称',
          key: 'supplierName'
        },
        {
          col: '采购类型',
          key: 'purchaseType'
        },
        {
          col: '合同名称',
          key: 'contractName'
        },
        {
          col: '材料编码',
          key: 'materialCode'
        },
        {
          col: '材料名称',
          key: 'materialName'
        },
        {
          col: '规格型号',
          key: 'materialSpec'
        },
        {
          col: '质量标准',
          key: 'qualityStandard'
        },
        {
          col: '计量单位',
          key: 'unit'
        },
        {
          col: '进场数量',
          key: 'siteEntryQuantity',
          type: 'number'
        },
        {
          col: '实收数量',
          key: 'actualQuantity',
          type: 'number'
        },
        {
          col: '外观质量描述',
          key: 'appearanceDescription'
        },
        {
          col: '备注',
          key: 'remark'
        }
      ],
      dataStartRow: 2
    }
  })
);
