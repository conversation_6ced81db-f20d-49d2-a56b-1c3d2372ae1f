const config = {
  fileNamePrefix: '物资进场验收单',
  useStream: false,
  qrCode: {
    type: 'pdf',
    width: 72,
    height: 72,
    position: 'lt',
    margins: {
      top: 2,
      left: 2
    }
  },
  sheets: {
    目录: {
      serviceName: 'ecost',
      method: 'get',
      path: '/material-incoming-inspection/inspection-bill/list/excel-sheet',
      queryProps: ['orgId'],
      exportFn: 'sheetName',
      sheetDesc: {
        sheetName: 'sheetName',
        sheetKey: 'sheet',
        props: ['id']
      },
      sheetData: [
        {
          titles: [
            [
              {
                text: '陕西建工第十六建设有限公司',
                style: {
                  alignment: {
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 14,
                    bold: true
                  }
                },
                colSpan: 2
              }
            ]
          ],
          columns: [
            {
              header: {
                text: '序号',
                width: 100,
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                }
              },
              key: 'order'
            },
            {
              header: {
                text: '单据编码',
                width: 100,
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                }
              },
              key: 'code',
              linkSheet: true // 是否是跳转到其他sheet
            }
          ]
        }
      ]
    },
    sheet: {
      serviceName: 'ecost',
      method: 'get',
      path: '/material-incoming-inspection/inspection-bill/detail/sheet-data',
      queryProps: ['id'],
      enablePrint: true,
      pageSetting: {
        paperSize: 9,
        orientation: 'landscape',
        margins: {
          top: 1.91,
          right: 1.78,
          bottom: 1.91,
          left: 1.78,
          header: 1
        },
        printTitlesRow: '1:5'
      },
      sheetData: [
        {
          titles: [
            [
              {
                text: '陕西建工第十六建设有限公司',
                style: {
                  alignment: {
                    horizontal: 'center',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 14,
                    bold: true
                  }
                },
                colSpan: 8,
                height: 27
              }
            ],
            [
              {
                text: '物资进场验收记录',
                style: {
                  alignment: {
                    horizontal: 'center',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 14,
                    bold: true
                  }
                },
                colSpan: 8,
                height: 27
              }
            ],
            [
              {
                text: '项目名称：',
                style: {
                  alignment: {
                    horizontal: 'left',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 11
                  }
                },
                colSpan: 5,
                height: 23
              },
              {
                text: '单据编码：',
                style: {
                  alignment: {
                    horizontal: 'left',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 11
                  }
                },
                colSpan: 3
              }
            ],
            [
              {
                text: '供应商名称：',
                style: {
                  alignment: {
                    horizontal: 'left',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 11
                  }
                },
                colSpan: 5,
                height: 23
              },
              {
                text: '进场日期：',
                style: {
                  alignment: {
                    horizontal: 'left',
                    vertical: 'middle'
                  },
                  font: {
                    name: '仿宋',
                    size: 11
                  }
                },
                colSpan: 3
              }
            ]
          ],
          columns: [
            {
              header: {
                text: '物资名称',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                height: 30,
                width: 21.33
              },
              key: 'materialName',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '规格型号',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 21.33
              },
              key: 'materialSpec',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '质量标准',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 21.33
              },
              key: 'quantityStandard',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '计量单位',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 9.33
              },
              key: 'unit',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '进场数量',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 9.33
              },
              key: 'entrySiteQuantity',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '实收数量',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 9.33
              },
              key: 'actualQuantity',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '外观质量描述',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 26.33
              },
              key: 'appearanceQualityDescription',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            },
            {
              header: {
                text: '备  注',
                style: {
                  alignment: {
                    vertical: 'middle',
                    horizontal: 'center'
                  },
                  font: {
                    name: '仿宋',
                    size: 10,
                    bold: true
                  },
                  border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                  }
                },
                width: 6.89
              },
              key: 'remark',
              height: 25,
              style: {
                font: {
                  name: '仿宋',
                  size: 10
                }
              }
            }
          ],
          pageFooter: {
            differentOddEven: false,
            vertical: 'top',
            oddContents: '送货人： 材料员： 施工员： 分包材料员：',
            margin: 30
          }
        }
      ]
    }
  }
};

console.log(JSON.stringify(config));
