import { RedisClientType, RedisModule } from '@ewing/infra-cloud-sdk';
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';

import { CatchEverythingFilter } from '@/common/filters/catch-everything.filter';
import { ConfigModule } from '@/config/config.module';
import { AuthModule } from '@/modules/auth/auth.module';

import { ExcelModule } from './modules/excel/excel.module';

// import { SubcontractingStandardsModule } from '@/modules/subcontracting-standards/subcontracting-standards.module';

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    ExcelModule,
    RedisModule.forRoot(RedisClientType.COMMON)
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: CatchEverythingFilter
    }
  ]
})
export class AppModule {}
