{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/modules/*": ["src/modules/*"], "@/prisma/*": ["prisma/*"], "@/public/*": ["src/public/*"]}, "incremental": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "strictBindCallApply": true, "strictFunctionTypes": true, "useUnknownInCatchVariables": true, "lib": ["ES2023"], "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "./node_modules/.pnpm/node_modules/@types"]}, "include": ["src/**/*", "test/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist"]}